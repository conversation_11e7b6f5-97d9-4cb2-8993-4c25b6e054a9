"""
Network utilities for connectivity checking and graceful error handling
"""

import socket
import requests
import time
import logging
from typing import Op<PERSON>, Dict, Tuple
from urllib.parse import urlparse


class NetworkChecker:
    """Network connectivity checker for Wayback Machine services"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or self._setup_logger()
        self.wayback_hosts = [
            'web.archive.org',
            'archive.org'
        ]
        self.last_check_time = 0
        self.last_check_result = None
        self.check_interval = 300  # 5 minutes cache
        
    def _setup_logger(self) -> logging.Logger:
        """Setup logger for network checker"""
        logger = logging.getLogger('network_checker')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def check_dns_resolution(self, hostname: str) -> bool:
        """Check if hostname can be resolved"""
        try:
            socket.gethostbyname(hostname)
            return True
        except socket.gaierror:
            return False
    
    def check_port_connectivity(self, hostname: str, port: int = 443, timeout: int = 10) -> bool:
        """Check if can connect to specific port"""
        try:
            sock = socket.create_connection((hostname, port), timeout)
            sock.close()
            return True
        except (socket.timeout, socket.error, OSError):
            return False
    
    def check_http_connectivity(self, url: str, timeout: int = 10) -> Tuple[bool, Optional[int]]:
        """Check HTTP connectivity and return status code"""
        try:
            response = requests.head(url, timeout=timeout, allow_redirects=True)
            return True, response.status_code
        except requests.exceptions.RequestException:
            return False, None
    
    def comprehensive_connectivity_check(self, force_check: bool = False) -> Dict:
        """Perform comprehensive connectivity check with caching"""
        current_time = time.time()
        
        # Use cached result if recent and not forced
        if (not force_check and 
            self.last_check_result and 
            current_time - self.last_check_time < self.check_interval):
            return self.last_check_result
        
        self.logger.info("Performing network connectivity check...")
        
        results = {
            'overall_status': True,
            'dns_resolution': {},
            'port_connectivity': {},
            'http_connectivity': {},
            'recommendations': [],
            'timestamp': current_time
        }
        
        # Check DNS resolution
        for host in self.wayback_hosts:
            dns_ok = self.check_dns_resolution(host)
            results['dns_resolution'][host] = dns_ok
            if not dns_ok:
                results['overall_status'] = False
                results['recommendations'].append(f"DNS resolution failed for {host}")
        
        # Check port connectivity
        for host in self.wayback_hosts:
            if results['dns_resolution'].get(host, False):
                port_ok = self.check_port_connectivity(host, 443)
                results['port_connectivity'][host] = port_ok
                if not port_ok:
                    results['overall_status'] = False
                    results['recommendations'].append(f"Cannot connect to {host}:443 (HTTPS)")
        
        # Check HTTP connectivity
        test_urls = [
            'https://web.archive.org/',
            'https://archive.org/'
        ]
        
        for url in test_urls:
            host = urlparse(url).netloc
            if results['port_connectivity'].get(host, False):
                http_ok, status_code = self.check_http_connectivity(url)
                results['http_connectivity'][url] = {
                    'success': http_ok,
                    'status_code': status_code
                }
                if not http_ok:
                    results['overall_status'] = False
                    results['recommendations'].append(f"HTTP request failed for {url}")
        
        # Add specific recommendations
        if not results['overall_status']:
            if not any(results['dns_resolution'].values()):
                results['recommendations'].append("Check internet connection and DNS settings")
            elif not any(results['port_connectivity'].values()):
                results['recommendations'].append("Check firewall settings - HTTPS (port 443) may be blocked")
            else:
                results['recommendations'].append("Wayback Machine services may be temporarily unavailable")
        
        # Cache results
        self.last_check_time = current_time
        self.last_check_result = results
        
        # Log results
        if results['overall_status']:
            self.logger.info("✅ Network connectivity check passed")
        else:
            self.logger.warning("❌ Network connectivity issues detected")
            for rec in results['recommendations']:
                self.logger.warning(f"  - {rec}")
        
        return results
    
    def is_wayback_accessible(self, force_check: bool = False) -> bool:
        """Quick check if Wayback Machine is accessible"""
        results = self.comprehensive_connectivity_check(force_check)
        return results['overall_status']
    
    def get_connectivity_status_message(self) -> str:
        """Get user-friendly connectivity status message"""
        if not self.last_check_result:
            self.comprehensive_connectivity_check()
        
        if self.last_check_result['overall_status']:
            return "🟢 Kết nối Wayback Machine: Bình thường"
        else:
            recommendations = self.last_check_result.get('recommendations', [])
            if recommendations:
                return f"🔴 Kết nối Wayback Machine: Có vấn đề - {recommendations[0]}"
            else:
                return "🔴 Kết nối Wayback Machine: Có vấn đề"


class SmartErrorHandler:
    """Smart error handler that reduces log spam and provides better UX"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger('smart_error_handler')
        self.error_counts = {}
        self.last_error_times = {}
        self.suppression_threshold = 3  # Suppress after 3 similar errors
        self.suppression_window = 300   # 5 minutes
        
    def should_log_error(self, error_key: str) -> bool:
        """Determine if error should be logged based on frequency"""
        current_time = time.time()
        
        # Reset count if outside suppression window
        if (error_key in self.last_error_times and 
            current_time - self.last_error_times[error_key] > self.suppression_window):
            self.error_counts[error_key] = 0
        
        # Update tracking
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        self.last_error_times[error_key] = current_time
        
        # Log first few occurrences, then suppress
        return self.error_counts[error_key] <= self.suppression_threshold
    
    def log_network_error(self, domain: str, error: Exception, attempt: int = 1):
        """Log network error with smart suppression"""
        error_key = f"network_error_{type(error).__name__}"
        
        if self.should_log_error(error_key):
            if self.error_counts[error_key] == self.suppression_threshold:
                self.logger.warning(
                    f"Network connectivity issues detected for {domain}. "
                    f"Suppressing similar errors for {self.suppression_window/60:.0f} minutes."
                )
            else:
                self.logger.warning(
                    f"Network error for {domain} (attempt {attempt}): {str(error)[:100]}..."
                )
    
    def get_error_summary(self) -> Dict:
        """Get summary of suppressed errors"""
        return {
            'error_counts': self.error_counts.copy(),
            'suppressed_errors': {
                k: v for k, v in self.error_counts.items() 
                if v > self.suppression_threshold
            }
        }


# Global instances
network_checker = NetworkChecker()
smart_error_handler = SmartErrorHandler()


def check_wayback_connectivity() -> bool:
    """Quick function to check Wayback Machine connectivity"""
    return network_checker.is_wayback_accessible()


def get_connectivity_message() -> str:
    """Get connectivity status message for UI"""
    return network_checker.get_connectivity_status_message()


def log_network_error_smart(domain: str, error: Exception, attempt: int = 1):
    """Smart network error logging"""
    smart_error_handler.log_network_error(domain, error, attempt)
