"""
Enhanced Error Handling and Rate Limiting for Wayback Machine API
"""

import time
import logging
import random
from typing import Optional, Callable, Any, Dict
from functools import wraps
import requests
from waybackpy.exceptions import WaybackError
import backoff


class WaybackRateLimiter:
    """Advanced rate limiter for Wayback Machine API calls"""
    
    def __init__(self):
        # Rate limits based on Wayback Machine API documentation
        self.search_calls_per_second = 0.8
        self.memento_calls_per_second = 8.0
        self.timemap_calls_per_second = 1.33
        
        # Tracking variables
        self.last_call_times = {
            'search': 0,
            'memento': 0,
            'timemap': 0
        }
        
        # Call counters for monitoring
        self.call_counts = {
            'search': 0,
            'memento': 0,
            'timemap': 0
        }
        
        # Adaptive delay multiplier
        self.delay_multiplier = 1.0
        self.consecutive_errors = 0
        
    def wait_for_rate_limit(self, api_type: str = 'search'):
        """Wait for rate limit with adaptive delays"""
        current_time = time.time()
        
        # Get appropriate rate limit
        if api_type == 'search':
            calls_per_second = self.search_calls_per_second
        elif api_type == 'memento':
            calls_per_second = self.memento_calls_per_second
        elif api_type == 'timemap':
            calls_per_second = self.timemap_calls_per_second
        else:
            calls_per_second = self.search_calls_per_second  # Default to most restrictive
        
        # Calculate minimum delay
        min_delay = (1.0 / calls_per_second) * self.delay_multiplier
        
        # Add jitter to prevent thundering herd
        jitter = random.uniform(0.1, 0.3)
        min_delay += jitter
        
        # Check time since last call
        time_since_last = current_time - self.last_call_times.get(api_type, 0)
        
        if time_since_last < min_delay:
            sleep_time = min_delay - time_since_last
            time.sleep(sleep_time)
        
        # Update tracking
        self.last_call_times[api_type] = time.time()
        self.call_counts[api_type] += 1
    
    def increase_delay(self):
        """Increase delay multiplier after errors"""
        self.consecutive_errors += 1
        self.delay_multiplier = min(self.delay_multiplier * 1.5, 5.0)
        
    def reset_delay(self):
        """Reset delay multiplier after successful calls"""
        self.consecutive_errors = 0
        self.delay_multiplier = max(self.delay_multiplier * 0.9, 1.0)
    
    def get_stats(self) -> Dict:
        """Get rate limiter statistics"""
        return {
            'call_counts': self.call_counts.copy(),
            'delay_multiplier': self.delay_multiplier,
            'consecutive_errors': self.consecutive_errors
        }


class WaybackErrorHandler:
    """Enhanced error handling for Wayback Machine operations"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or self._setup_logger()
        self.rate_limiter = WaybackRateLimiter()
        
        # Error tracking
        self.error_counts = {
            'rate_limit': 0,
            'network': 0,
            'wayback': 0,
            'timeout': 0,
            'other': 0
        }
        
        # Recovery strategies
        self.recovery_strategies = {
            'rate_limit': self._handle_rate_limit_error,
            'network': self._handle_network_error,
            'wayback': self._handle_wayback_error,
            'timeout': self._handle_timeout_error
        }
    
    def _setup_logger(self) -> logging.Logger:
        """Setup error handler logger"""
        logger = logging.getLogger('wayback_error_handler')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _handle_rate_limit_error(self, error: Exception, attempt: int) -> float:
        """Handle rate limit errors with exponential backoff"""
        self.error_counts['rate_limit'] += 1
        self.rate_limiter.increase_delay()
        
        # Exponential backoff with jitter
        base_delay = min(2 ** attempt, 60)  # Cap at 60 seconds
        jitter = random.uniform(0.5, 1.5)
        delay = base_delay * jitter
        
        self.logger.warning(f"Rate limit hit (attempt {attempt}), waiting {delay:.2f}s")
        return delay
    
    def _handle_network_error(self, error: Exception, attempt: int) -> float:
        """Handle network-related errors"""
        self.error_counts['network'] += 1
        
        # Progressive delay for network issues
        delay = min(5 * attempt, 30)  # Cap at 30 seconds
        self.logger.warning(f"Network error (attempt {attempt}): {str(error)}, waiting {delay}s")
        return delay
    
    def _handle_wayback_error(self, error: Exception, attempt: int) -> float:
        """Handle Wayback Machine specific errors"""
        self.error_counts['wayback'] += 1
        
        # Moderate delay for Wayback errors
        delay = min(3 * attempt, 20)  # Cap at 20 seconds
        self.logger.warning(f"Wayback error (attempt {attempt}): {str(error)}, waiting {delay}s")
        return delay
    
    def _handle_timeout_error(self, error: Exception, attempt: int) -> float:
        """Handle timeout errors"""
        self.error_counts['timeout'] += 1
        
        # Longer delay for timeouts
        delay = min(10 * attempt, 60)  # Cap at 60 seconds
        self.logger.warning(f"Timeout error (attempt {attempt}), waiting {delay}s")
        return delay
    
    def classify_error(self, error: Exception) -> str:
        """Classify error type for appropriate handling"""
        error_str = str(error).lower()

        # Check for rate limit indicators in error message
        if ('429' in error_str or 'rate limit' in error_str or
            'too many requests' in error_str or 'quota exceeded' in error_str):
            return 'rate_limit'
        elif isinstance(error, (requests.exceptions.ConnectionError,
                               requests.exceptions.Timeout,
                               requests.exceptions.RequestException)):
            if 'timeout' in error_str:
                return 'timeout'
            return 'network'
        elif isinstance(error, WaybackError):
            return 'wayback'
        elif 'timeout' in error_str:
            return 'timeout'
        else:
            return 'other'
    
    def should_retry(self, error: Exception, attempt: int, max_attempts: int = 5) -> bool:
        """Determine if operation should be retried"""
        if attempt >= max_attempts:
            return False
        
        error_type = self.classify_error(error)
        
        # Don't retry certain types of errors
        if error_type == 'other':
            error_str = str(error).lower()
            # Don't retry on authentication or permission errors
            if any(keyword in error_str for keyword in ['auth', 'permission', 'forbidden', '403']):
                return False
        
        return True
    
    def handle_error(self, error: Exception, attempt: int) -> float:
        """Handle error and return delay time"""
        error_type = self.classify_error(error)
        
        if error_type in self.recovery_strategies:
            return self.recovery_strategies[error_type](error, attempt)
        else:
            self.error_counts['other'] += 1
            delay = min(2 * attempt, 10)  # Conservative delay for unknown errors
            self.logger.error(f"Unknown error type (attempt {attempt}): {str(error)}, waiting {delay}s")
            return delay
    
    def get_error_stats(self) -> Dict:
        """Get error statistics"""
        return {
            'error_counts': self.error_counts.copy(),
            'rate_limiter_stats': self.rate_limiter.get_stats()
        }


def with_error_handling(max_attempts: int = 5, api_type: str = 'search'):
    """Decorator for adding error handling and rate limiting to functions"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            # Get or create error handler
            if hasattr(wrapper, '_error_handler'):
                error_handler = wrapper._error_handler
            else:
                error_handler = WaybackErrorHandler()
                wrapper._error_handler = error_handler
            
            last_error = None
            
            for attempt in range(1, max_attempts + 1):
                try:
                    # Apply rate limiting
                    error_handler.rate_limiter.wait_for_rate_limit(api_type)
                    
                    # Execute function
                    result = func(*args, **kwargs)
                    
                    # Reset delay on success
                    error_handler.rate_limiter.reset_delay()
                    
                    return result
                    
                except Exception as error:
                    last_error = error
                    
                    # Check if we should retry
                    if not error_handler.should_retry(error, attempt, max_attempts):
                        break
                    
                    # Handle error and get delay
                    delay = error_handler.handle_error(error, attempt)
                    
                    # Sleep before retry
                    if attempt < max_attempts:
                        time.sleep(delay)
            
            # If we get here, all attempts failed
            error_handler.logger.error(f"All {max_attempts} attempts failed for {func.__name__}")
            raise last_error
        
        return wrapper
    return decorator


class CircuitBreaker:
    """Circuit breaker pattern for Wayback Machine API calls"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
        
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection"""
        if self.state == 'OPEN':
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = 'HALF_OPEN'
            else:
                raise Exception("Circuit breaker is OPEN")
        
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure()
            raise
    
    def _on_success(self):
        """Handle successful call"""
        self.failure_count = 0
        self.state = 'CLOSED'
    
    def _on_failure(self):
        """Handle failed call"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = 'OPEN'


# Global error handler instance
global_error_handler = WaybackErrorHandler()


def get_error_handler() -> WaybackErrorHandler:
    """Get global error handler instance"""
    return global_error_handler
