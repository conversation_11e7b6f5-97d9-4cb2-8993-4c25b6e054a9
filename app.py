"""
Streamlit App for SEO Domain Analysis using Wayback Machine
"""

import streamlit as st
import pandas as pd
import io
import time
from datetime import datetime
from typing import List, Dict
import pyperclip
from wayback_analyzer import WaybackAnalyzer
from data_processor import DataProcessor


def init_session_state():
    """Initialize session state variables"""
    if 'analyzer' not in st.session_state:
        st.session_state.analyzer = WaybackAnalyzer()
    
    if 'analysis_results' not in st.session_state:
        st.session_state.analysis_results = []
    
    if 'is_processing' not in st.session_state:
        st.session_state.is_processing = False
    
    if 'processed_count' not in st.session_state:
        st.session_state.processed_count = 0
    
    if 'total_count' not in st.session_state:
        st.session_state.total_count = 0


def validate_and_clean_domains(domains_input: str) -> List[str]:
    """Validate and clean domain list using DataProcessor"""
    return DataProcessor.extract_domains_from_text(domains_input)


def parse_uploaded_file(uploaded_file) -> List[str]:
    """Parse uploaded file and extract domains using DataProcessor"""
    domains = []

    try:
        if uploaded_file.type == "text/plain":
            # Handle .txt files
            content = uploaded_file.read()
            text_content = content.decode('utf-8')
            domains = DataProcessor.extract_domains_from_text(text_content)

        elif uploaded_file.type in ["text/csv", "application/vnd.ms-excel"]:
            # Handle .csv files
            content = uploaded_file.read()
            domains = DataProcessor.extract_domains_from_csv(content)

    except Exception as e:
        st.error(f"Lỗi khi đọc file: {str(e)}")

    return domains


def format_status_codes(status_codes: Dict[str, int]) -> str:
    """Format status codes with colors using DataProcessor"""
    return DataProcessor.format_status_codes_display(status_codes)


def create_results_dataframe(results: List[Dict]) -> pd.DataFrame:
    """Create formatted DataFrame from results"""
    if not results:
        return pd.DataFrame()
    
    data = []
    for i, result in enumerate(results, 1):
        # Format time range
        time_range = ""
        if result['first_date'] and result['last_date']:
            time_range = f"{result['first_date']} - {result['last_date']}"
        elif result['first_date']:
            time_range = result['first_date']
        
        # Format status codes
        status_display = format_status_codes(result['status_codes'])
        
        data.append({
            'STT': i,
            'Domain': result['domain'],
            'Month Col': result['month_col'],
            'Snapshot': result['total_snapshots'],
            'Thời gian': time_range,
            'StatusCode': status_display,
            'Lỗi': result.get('error', '')
        })
    
    return pd.DataFrame(data)


def progress_callback(progress: float, current: int, total: int):
    """Callback function for progress updates"""
    st.session_state.processed_count = current
    st.session_state.total_count = total


def main():
    """Main Streamlit application"""
    st.set_page_config(
        page_title="SEO Domain Analyzer - Wayback Machine",
        page_icon="🔍",
        layout="wide"
    )
    
    init_session_state()
    
    st.title("🔍 SEO Domain Analyzer - Wayback Machine")
    st.markdown("Phân tích thông tin SEO của các domain thông qua dữ liệu từ Wayback Machine")
    
    # Sidebar for controls
    with st.sidebar:
        st.header("⚙️ Điều khiển")
        
        if st.button("🔄 Reset", type="secondary"):
            st.session_state.analysis_results = []
            st.session_state.is_processing = False
            st.session_state.processed_count = 0
            st.session_state.total_count = 0
            st.rerun()
    
    # Input Section
    st.header("📝 Nhập danh sách Domain")
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        domains_input = st.text_area(
            "Nhập danh sách domain (mỗi domain một dòng):",
            height=200,
            placeholder="example.com\ngoogle.com\nfacebook.com"
        )
    
    with col2:
        uploaded_file = st.file_uploader(
            "Hoặc upload file (.txt, .csv):",
            type=['txt', 'csv'],
            help="File .txt: mỗi domain một dòng\nFile .csv: domain ở cột đầu tiên"
        )
    
    # Process domains
    domains = []
    
    if domains_input.strip():
        domains = validate_and_clean_domains(domains_input)
    
    if uploaded_file is not None:
        file_domains = parse_uploaded_file(uploaded_file)
        # Merge with text input, avoiding duplicates
        for domain in file_domains:
            if domain not in domains:
                domains.append(domain)
    
    # Display domain count and validation
    if domains:
        st.success(f"✅ Tìm thấy {len(domains)} domain hợp lệ")
        if len(domains) > 5000:
            st.warning("⚠️ Chỉ hỗ trợ tối đa 5000 domain. Sẽ xử lý 5000 domain đầu tiên.")
            domains = domains[:5000]
    elif domains_input.strip() or uploaded_file is not None:
        st.error("❌ Không tìm thấy domain hợp lệ nào")
    
    # Analysis controls
    if domains and not st.session_state.is_processing:
        if st.button("🚀 Bắt đầu phân tích", type="primary"):
            st.session_state.is_processing = True
            st.session_state.analysis_results = []
            st.rerun()
    
    # Processing section
    if st.session_state.is_processing:
        st.header("⏳ Đang xử lý...")
        
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        # Start analysis
        try:
            results = st.session_state.analyzer.analyze_domains_batch(
                domains, 
                progress_callback=progress_callback
            )
            
            # Update progress
            for i, result in enumerate(results):
                progress = (i + 1) / len(domains)
                progress_bar.progress(progress)
                status_text.text(f"Đã xử lý: {i + 1}/{len(domains)} domain")
                time.sleep(0.1)  # Small delay for UI update
            
            st.session_state.analysis_results = results
            st.session_state.is_processing = False
            st.success("✅ Hoàn thành phân tích!")
            st.rerun()
            
        except Exception as e:
            st.error(f"❌ Lỗi trong quá trình phân tích: {str(e)}")
            st.session_state.is_processing = False
    
    # Results section
    if st.session_state.analysis_results:
        st.header("📊 Kết quả phân tích")
        
        df = create_results_dataframe(st.session_state.analysis_results)
        
        if not df.empty:
            # Display results table
            st.dataframe(
                df,
                use_container_width=True,
                hide_index=True
            )
            
            # Export controls
            col1, col2, col3 = st.columns(3)
            
            with col1:
                # CSV download using DataProcessor
                csv_data = DataProcessor.export_to_csv(st.session_state.analysis_results)

                st.download_button(
                    label="📥 Download CSV",
                    data=csv_data,
                    file_name=f"seo_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )

            with col2:
                # Excel download using DataProcessor
                excel_data = DataProcessor.export_to_excel(st.session_state.analysis_results)

                st.download_button(
                    label="📥 Download Excel",
                    data=excel_data,
                    file_name=f"seo_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                )
            
            with col3:
                # Copy to clipboard
                if st.button("📋 Copy to Clipboard"):
                    try:
                        clipboard_data = df.to_string(index=False)
                        pyperclip.copy(clipboard_data)
                        st.success("✅ Đã copy vào clipboard!")
                    except Exception as e:
                        st.error(f"❌ Lỗi copy: {str(e)}")
            
            # Statistics using DataProcessor
            st.subheader("📈 Thống kê tổng quan")

            stats = DataProcessor.create_summary_stats(st.session_state.analysis_results)

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("Tổng domain", stats.get('total_domains', 0))

            with col2:
                st.metric("Thành công", stats.get('successful_analyses', 0))

            with col3:
                st.metric("Thất bại", stats.get('failed_analyses', 0))

            with col4:
                success_rate = stats.get('success_rate', 0)
                st.metric("Tỷ lệ thành công", f"{success_rate:.1f}%")

            # Additional statistics
            col5, col6 = st.columns(2)

            with col5:
                st.metric("Tổng snapshots", stats.get('total_snapshots', 0))
                st.metric("TB snapshots/domain", f"{stats.get('avg_snapshots_per_domain', 0):.1f}")

            with col6:
                st.metric("Tổng tháng unique", stats.get('total_unique_months', 0))
                st.metric("TB tháng/domain", f"{stats.get('avg_months_per_domain', 0):.1f}")

            # Auto-save functionality
            if st.session_state.analysis_results:
                try:
                    temp_filename = DataProcessor.save_temp_results(
                        st.session_state.analysis_results,
                        f"temp_seo_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                    )
                    st.info(f"💾 Kết quả đã được lưu tạm thời: {temp_filename}")
                except Exception as e:
                    st.warning(f"⚠️ Không thể lưu tạm thời: {str(e)}")


if __name__ == "__main__":
    main()
