"""
Streamlit App for SEO Domain Analysis using Wayback Machine
"""

import streamlit as st
import pandas as pd
import io
from datetime import datetime
from typing import List, Dict
import pyperclip
from wayback_analyzer import WaybackAnalyzer
from data_processor import DataProcessor
from network_utils import get_connectivity_message
from concurrent_analyzer import Concurrent<PERSON><PERSON>back<PERSON><PERSON>yzer
from thread_safe_rate_limiter import get_global_rate_limiter


def init_session_state():
    """Initialize session state variables"""
    if 'analyzer' not in st.session_state:
        st.session_state.analyzer = WaybackAnalyzer()

    if 'concurrent_analyzer' not in st.session_state:
        st.session_state.concurrent_analyzer = None

    if 'analysis_results' not in st.session_state:
        st.session_state.analysis_results = []

    if 'is_processing' not in st.session_state:
        st.session_state.is_processing = False

    if 'is_paused' not in st.session_state:
        st.session_state.is_paused = False

    if 'processed_count' not in st.session_state:
        st.session_state.processed_count = 0

    if 'total_count' not in st.session_state:
        st.session_state.total_count = 0

    if 'max_workers' not in st.session_state:
        st.session_state.max_workers = 3

    if 'use_concurrent' not in st.session_state:
        st.session_state.use_concurrent = True

    if 'processing_stats' not in st.session_state:
        st.session_state.processing_stats = None


def validate_and_clean_domains(domains_input: str) -> List[str]:
    """Validate and clean domain list using DataProcessor"""
    return DataProcessor.extract_domains_from_text(domains_input)


def parse_uploaded_file(uploaded_file) -> List[str]:
    """Parse uploaded file and extract domains using DataProcessor"""
    domains = []

    try:
        if uploaded_file.type == "text/plain":
            # Handle .txt files
            content = uploaded_file.read()
            text_content = content.decode('utf-8')
            domains = DataProcessor.extract_domains_from_text(text_content)

        elif uploaded_file.type in ["text/csv", "application/vnd.ms-excel"]:
            # Handle .csv files
            content = uploaded_file.read()
            domains = DataProcessor.extract_domains_from_csv(content)

    except Exception as e:
        st.error(f"Lỗi khi đọc file: {str(e)}")

    return domains


def format_status_codes(status_codes: Dict[str, int]) -> str:
    """Format status codes with colors using DataProcessor"""
    return DataProcessor.format_status_codes_display(status_codes)


def create_results_dataframe(results: List[Dict]) -> pd.DataFrame:
    """Create formatted DataFrame from results"""
    if not results:
        return pd.DataFrame()
    
    data = []
    for i, result in enumerate(results, 1):
        # Format time range
        time_range = ""
        if result['first_date'] and result['last_date']:
            time_range = f"{result['first_date']} - {result['last_date']}"
        elif result['first_date']:
            time_range = result['first_date']
        
        # Format status codes
        status_display = format_status_codes(result['status_codes'])
        
        data.append({
            'STT': i,
            'Domain': result['domain'],
            'Month Col': result['month_col'],
            'Snapshot': result['total_snapshots'],
            'Thời gian': time_range,
            'StatusCode': status_display,
            'Lỗi': result.get('error', '')
        })
    
    return pd.DataFrame(data)


def progress_callback(progress: float, current: int, total: int):
    """Callback function for progress updates"""
    st.session_state.processed_count = current
    st.session_state.total_count = total


def main():
    """Main Streamlit application"""
    st.set_page_config(
        page_title="SEO Domain Analyzer - Wayback Machine",
        page_icon="🔍",
        layout="wide"
    )
    
    init_session_state()
    
    st.title("🔍 SEO Domain Analyzer - Wayback Machine")
    st.markdown("Phân tích thông tin SEO của các domain thông qua dữ liệu từ Wayback Machine")
    
    # Sidebar for controls
    with st.sidebar:
        st.header("⚙️ Điều khiển")

        # Network status
        connectivity_msg = get_connectivity_message()
        st.info(connectivity_msg)

        # Performance settings
        st.subheader("🚀 Cài đặt hiệu suất")

        # Concurrent processing toggle
        use_concurrent = st.checkbox(
            "Sử dụng xử lý đồng thời",
            value=st.session_state.use_concurrent,
            help="Tăng tốc độ xử lý bằng cách sử dụng nhiều threads"
        )
        st.session_state.use_concurrent = use_concurrent

        # Worker count slider
        if use_concurrent:
            rate_limiter = get_global_rate_limiter()
            optimal_workers = rate_limiter.get_optimal_thread_count()

            max_workers = st.slider(
                "Số threads đồng thời",
                min_value=1,
                max_value=10,
                value=st.session_state.max_workers,
                help=f"Khuyến nghị: {optimal_workers} threads dựa trên rate limits"
            )
            st.session_state.max_workers = max_workers

            if max_workers > optimal_workers:
                st.warning(f"⚠️ Số threads vượt quá khuyến nghị ({optimal_workers}). Hiệu suất có thể bị giới hạn bởi API rate limits.")

            # Rate limiter stats
            if st.session_state.is_processing and st.session_state.concurrent_analyzer:
                stats = st.session_state.concurrent_analyzer.get_statistics()
                rate_stats = stats.get('rate_limiter', {})
                current_state = rate_stats.get('current_state', {})

                st.metric("Rate hiện tại", f"{current_state.get('current_rate', 0):.2f} calls/s")
                st.metric("Tokens khả dụng", f"{current_state.get('available_tokens', 0):.1f}")

        # Control buttons
        st.subheader("🎮 Điều khiển")

        col1, col2 = st.columns(2)

        with col1:
            if st.button("🔄 Reset", type="secondary"):
                # Stop any running processing
                if st.session_state.concurrent_analyzer:
                    st.session_state.concurrent_analyzer.stop_processing()

                st.session_state.analysis_results = []
                st.session_state.is_processing = False
                st.session_state.is_paused = False
                st.session_state.processed_count = 0
                st.session_state.total_count = 0
                st.session_state.concurrent_analyzer = None
                st.rerun()

        with col2:
            if st.session_state.is_processing:
                if st.session_state.is_paused:
                    if st.button("▶️ Tiếp tục", type="primary"):
                        if st.session_state.concurrent_analyzer:
                            st.session_state.concurrent_analyzer.resume_processing()
                            st.session_state.is_paused = False
                            st.rerun()
                else:
                    if st.button("⏸️ Tạm dừng", type="secondary"):
                        if st.session_state.concurrent_analyzer:
                            st.session_state.concurrent_analyzer.pause_processing()
                            st.session_state.is_paused = True
                            st.rerun()
    
    # Input Section
    st.header("📝 Nhập danh sách Domain")
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        domains_input = st.text_area(
            "Nhập danh sách domain (mỗi domain một dòng):",
            height=200,
            placeholder="example.com\ngoogle.com\nfacebook.com"
        )
    
    with col2:
        uploaded_file = st.file_uploader(
            "Hoặc upload file (.txt, .csv):",
            type=['txt', 'csv'],
            help="File .txt: mỗi domain một dòng\nFile .csv: domain ở cột đầu tiên"
        )
    
    # Process domains
    domains = []
    
    if domains_input.strip():
        domains = validate_and_clean_domains(domains_input)
    
    if uploaded_file is not None:
        file_domains = parse_uploaded_file(uploaded_file)
        # Merge with text input, avoiding duplicates
        for domain in file_domains:
            if domain not in domains:
                domains.append(domain)
    
    # Display domain count and validation
    if domains:
        st.success(f"✅ Tìm thấy {len(domains)} domain hợp lệ")
        if len(domains) > 5000:
            st.warning("⚠️ Chỉ hỗ trợ tối đa 5000 domain. Sẽ xử lý 5000 domain đầu tiên.")
            domains = domains[:5000]
    elif domains_input.strip() or uploaded_file is not None:
        st.error("❌ Không tìm thấy domain hợp lệ nào")
    
    # Analysis controls
    if domains and not st.session_state.is_processing:
        # Show network status before analysis
        connectivity_msg = get_connectivity_message()
        if "🔴" in connectivity_msg:
            st.warning(f"⚠️ {connectivity_msg}")
            st.warning("Phân tích có thể thất bại do vấn đề kết nối. Vui lòng kiểm tra internet và firewall.")

        # Show processing mode info
        if st.session_state.use_concurrent:
            st.info(f"🚀 Sẽ sử dụng {st.session_state.max_workers} threads đồng thời để tăng tốc độ xử lý")
        else:
            st.info("🐌 Sẽ sử dụng xử lý tuần tự (chậm hơn nhưng ổn định)")

        if st.button("🚀 Bắt đầu phân tích", type="primary"):
            st.session_state.is_processing = True
            st.session_state.analysis_results = []
            st.session_state.processed_count = 0
            st.session_state.total_count = len(domains)

            # Initialize concurrent analyzer if using concurrent mode
            if st.session_state.use_concurrent:
                st.session_state.concurrent_analyzer = ConcurrentWaybackAnalyzer(
                    max_workers=st.session_state.max_workers
                )

            st.rerun()
    
    # Processing section
    if st.session_state.is_processing:
        st.header("⏳ Đang xử lý...")

        # Create containers for real-time updates
        progress_container = st.container()
        stats_container = st.container()
        results_container = st.container()

        with progress_container:
            progress_bar = st.progress(0)
            status_text = st.empty()

        with stats_container:
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                completed_metric = st.empty()
            with col2:
                rate_metric = st.empty()
            with col3:
                eta_metric = st.empty()
            with col4:
                threads_metric = st.empty()

        # Real-time results table
        with results_container:
            st.subheader("📊 Kết quả real-time")
            results_table = st.empty()

        # Start analysis
        try:
            if st.session_state.use_concurrent and st.session_state.concurrent_analyzer:
                # Concurrent processing with real-time updates
                results = []

                def update_ui(completed, total, stats):
                    """Update UI with current progress"""
                    progress = completed / total if total > 0 else 0
                    progress_bar.progress(progress)

                    status_text.text(f"Đang xử lý: {completed}/{total} domains")

                    # Update metrics
                    completed_metric.metric("Hoàn thành", f"{completed}/{total}")
                    rate_metric.metric("Tốc độ", f"{stats.current_rate:.2f} domains/s")

                    if stats.estimated_time_remaining > 0:
                        eta_minutes = stats.estimated_time_remaining / 60
                        eta_metric.metric("Thời gian còn lại", f"{eta_minutes:.1f} phút")

                    threads_metric.metric("Threads hoạt động", stats.active_threads)

                    # Update results table
                    if results:
                        df = create_results_dataframe([r.result for r in results])
                        results_table.dataframe(df, use_container_width=True, hide_index=True)

                # Process domains with real-time updates
                for result in st.session_state.concurrent_analyzer.process_domains_concurrent(
                    domains,
                    progress_callback=update_ui
                ):
                    results.append(result)

                    # Check if processing was stopped
                    if not st.session_state.concurrent_analyzer.is_running:
                        break

                # Convert to expected format
                st.session_state.analysis_results = [r.result for r in results]

            else:
                # Sequential processing (fallback)
                def progress_callback_sequential(progress, current, total):
                    progress_bar.progress(progress)
                    status_text.text(f"Đã xử lý: {current}/{total} domain")
                    completed_metric.metric("Hoàn thành", f"{current}/{total}")

                results = st.session_state.analyzer.analyze_domains_batch(
                    domains,
                    progress_callback=progress_callback_sequential
                )

                st.session_state.analysis_results = results

            st.session_state.is_processing = False
            st.success("✅ Hoàn thành phân tích!")
            st.rerun()

        except Exception as e:
            st.error(f"❌ Lỗi trong quá trình phân tích: {str(e)}")
            st.session_state.is_processing = False
            if st.session_state.concurrent_analyzer:
                st.session_state.concurrent_analyzer.stop_processing()
    
    # Results section
    if st.session_state.analysis_results:
        st.header("📊 Kết quả phân tích")
        
        df = create_results_dataframe(st.session_state.analysis_results)
        
        if not df.empty:
            # Display results table
            st.dataframe(
                df,
                use_container_width=True,
                hide_index=True
            )
            
            # Export controls
            col1, col2, col3 = st.columns(3)
            
            with col1:
                # CSV download using DataProcessor
                csv_data = DataProcessor.export_to_csv(st.session_state.analysis_results)

                st.download_button(
                    label="📥 Download CSV",
                    data=csv_data,
                    file_name=f"seo_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )

            with col2:
                # Excel download using DataProcessor
                excel_data = DataProcessor.export_to_excel(st.session_state.analysis_results)

                st.download_button(
                    label="📥 Download Excel",
                    data=excel_data,
                    file_name=f"seo_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                )
            
            with col3:
                # Copy to clipboard
                if st.button("📋 Copy to Clipboard"):
                    try:
                        clipboard_data = df.to_string(index=False)
                        pyperclip.copy(clipboard_data)
                        st.success("✅ Đã copy vào clipboard!")
                    except Exception as e:
                        st.error(f"❌ Lỗi copy: {str(e)}")
            
            # Statistics using DataProcessor
            st.subheader("📈 Thống kê tổng quan")

            stats = DataProcessor.create_summary_stats(st.session_state.analysis_results)

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("Tổng domain", stats.get('total_domains', 0))

            with col2:
                st.metric("Thành công", stats.get('successful_analyses', 0))

            with col3:
                st.metric("Thất bại", stats.get('failed_analyses', 0))

            with col4:
                success_rate = stats.get('success_rate', 0)
                st.metric("Tỷ lệ thành công", f"{success_rate:.1f}%")

            # Additional statistics
            col5, col6 = st.columns(2)

            with col5:
                st.metric("Tổng snapshots", stats.get('total_snapshots', 0))
                st.metric("TB snapshots/domain", f"{stats.get('avg_snapshots_per_domain', 0):.1f}")

            with col6:
                st.metric("Tổng tháng unique", stats.get('total_unique_months', 0))
                st.metric("TB tháng/domain", f"{stats.get('avg_months_per_domain', 0):.1f}")

            # Performance statistics if concurrent processing was used
            if st.session_state.concurrent_analyzer:
                processing_stats = st.session_state.concurrent_analyzer.get_statistics()

                st.subheader("🚀 Thống kê hiệu suất")

                perf_col1, perf_col2, perf_col3 = st.columns(3)

                with perf_col1:
                    processing_info = processing_stats.get('processing', {})
                    avg_time = processing_info.get('average_time_per_domain', 0)
                    st.metric("TB thời gian/domain", f"{avg_time:.2f}s")

                with perf_col2:
                    current_rate = processing_info.get('current_rate', 0)
                    st.metric("Tốc độ xử lý", f"{current_rate:.2f} domains/s")

                with perf_col3:
                    control_info = processing_stats.get('control', {})
                    workers_used = control_info.get('max_workers', 0)
                    st.metric("Threads đã sử dụng", workers_used)

                # Rate limiter performance
                rate_stats = processing_stats.get('rate_limiter', {})
                if rate_stats:
                    with st.expander("📊 Chi tiết Rate Limiter"):
                        st.json(rate_stats)

            # Auto-save functionality
            if st.session_state.analysis_results:
                try:
                    temp_filename = DataProcessor.save_temp_results(
                        st.session_state.analysis_results,
                        f"temp_seo_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                    )
                    st.info(f"💾 Kết quả đã được lưu tạm thời: {temp_filename}")
                except Exception as e:
                    st.warning(f"⚠️ Không thể lưu tạm thời: {str(e)}")


if __name__ == "__main__":
    main()
