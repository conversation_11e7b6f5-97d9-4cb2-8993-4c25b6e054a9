"""
Concurrent Wayback Machine analyzer for high-performance domain processing
"""

import time
import threading
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed, Future
from typing import List, Dict, Optional, Callable, Any, Iterator
from dataclasses import dataclass
from queue import Queue, Empty
import traceback

from wayback_analyzer import Wayback<PERSON><PERSON>yzer
from thread_safe_rate_limiter import get_global_rate_limiter, RateLimitConfig
from network_utils import check_wayback_connectivity


@dataclass
class ProcessingResult:
    """Result from processing a single domain"""
    domain: str
    result: Dict[str, Any]
    processing_time: float
    thread_id: int
    timestamp: float


@dataclass
class ProcessingStats:
    """Statistics for concurrent processing"""
    total_domains: int = 0
    completed_domains: int = 0
    failed_domains: int = 0
    average_time_per_domain: float = 0.0
    estimated_time_remaining: float = 0.0
    current_rate: float = 0.0
    active_threads: int = 0


class ConcurrentWaybackAnalyzer:
    """High-performance concurrent analyzer for Wayback Machine data"""
    
    def __init__(self, max_workers: int = 3, user_agent: str = "SEO-Domain-Analyzer/1.0"):
        self.max_workers = max_workers
        self.user_agent = user_agent
        self.logger = self._setup_logger()
        
        # Rate limiting
        self.rate_limiter = get_global_rate_limiter()
        
        # Processing control
        self.is_running = False
        self.is_paused = False
        self.pause_event = threading.Event()
        self.pause_event.set()  # Start unpaused
        
        # Results management
        self.results_queue = Queue()
        self.completed_results = []
        self.processing_stats = ProcessingStats()
        
        # Thread management
        self.executor = None
        self.futures = []
        self.thread_analyzers = {}  # Thread-local analyzers
        
        # Timing
        self.start_time = None
        self.processing_times = []
        
    def _setup_logger(self) -> logging.Logger:
        """Setup logger for concurrent analyzer"""
        logger = logging.getLogger('concurrent_analyzer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _get_thread_analyzer(self) -> WaybackAnalyzer:
        """Get thread-local analyzer instance"""
        thread_id = threading.get_ident()
        
        if thread_id not in self.thread_analyzers:
            self.thread_analyzers[thread_id] = WaybackAnalyzer(self.user_agent)
        
        return self.thread_analyzers[thread_id]
    
    def _process_single_domain(self, domain: str, domain_index: int) -> ProcessingResult:
        """Process a single domain with rate limiting and error handling"""
        thread_id = threading.get_ident()
        start_time = time.time()
        
        try:
            # Wait if paused
            self.pause_event.wait()
            
            # Check if processing was stopped
            if not self.is_running:
                raise Exception("Processing stopped")
            
            # Acquire rate limit token
            if not self.rate_limiter.acquire(timeout=30.0):
                raise Exception("Rate limit timeout")
            
            # Get thread-local analyzer
            analyzer = self._get_thread_analyzer()
            
            # Process domain
            result = analyzer.analyze_domain(domain)
            
            # Report success to adaptive rate limiter
            self.rate_limiter.report_success()
            
            processing_time = time.time() - start_time
            
            return ProcessingResult(
                domain=domain,
                result=result,
                processing_time=processing_time,
                thread_id=thread_id,
                timestamp=time.time()
            )
            
        except Exception as e:
            # Check if it's a rate limit error
            if "rate limit" in str(e).lower() or "429" in str(e):
                self.rate_limiter.report_rate_limit_error()
            
            processing_time = time.time() - start_time
            
            # Create error result
            error_result = {
                'domain': domain,
                'month_col': 0,
                'total_snapshots': 0,
                'first_date': '',
                'last_date': '',
                'status_codes': {},
                'error': str(e)
            }
            
            self.logger.warning(f"Error processing {domain} in thread {thread_id}: {str(e)}")
            
            return ProcessingResult(
                domain=domain,
                result=error_result,
                processing_time=processing_time,
                thread_id=thread_id,
                timestamp=time.time()
            )
        
        finally:
            # Release thread from rate limiter tracking
            self.rate_limiter.release_thread(thread_id)
    
    def _update_statistics(self, result: ProcessingResult):
        """Update processing statistics"""
        self.processing_times.append(result.processing_time)
        self.processing_stats.completed_domains += 1
        
        if result.result.get('error'):
            self.processing_stats.failed_domains += 1
        
        # Calculate average processing time
        if self.processing_times:
            self.processing_stats.average_time_per_domain = sum(self.processing_times) / len(self.processing_times)
        
        # Calculate current rate (domains per second)
        if len(self.processing_times) >= 2:
            recent_times = self.processing_times[-10:]  # Last 10 domains
            if len(recent_times) >= 2:
                time_span = sum(recent_times)
                self.processing_stats.current_rate = len(recent_times) / time_span if time_span > 0 else 0
        
        # Estimate time remaining
        remaining_domains = self.processing_stats.total_domains - self.processing_stats.completed_domains
        if self.processing_stats.current_rate > 0:
            self.processing_stats.estimated_time_remaining = remaining_domains / self.processing_stats.current_rate
        elif self.processing_stats.average_time_per_domain > 0:
            self.processing_stats.estimated_time_remaining = remaining_domains * self.processing_stats.average_time_per_domain
    
    def process_domains_concurrent(
        self, 
        domains: List[str], 
        progress_callback: Optional[Callable] = None,
        result_callback: Optional[Callable] = None
    ) -> Iterator[ProcessingResult]:
        """
        Process domains concurrently with real-time results
        
        Args:
            domains: List of domains to process
            progress_callback: Called with (completed, total, stats)
            result_callback: Called with each ProcessingResult
            
        Yields:
            ProcessingResult objects as they complete
        """
        if not domains:
            return
        
        # Check network connectivity
        if not check_wayback_connectivity():
            self.logger.error("Cannot proceed - no network connectivity to Wayback Machine")
            return
        
        # Initialize processing
        self.is_running = True
        self.start_time = time.time()
        self.processing_stats = ProcessingStats(total_domains=len(domains))
        self.completed_results = []
        self.processing_times = []
        
        self.logger.info(f"Starting concurrent processing of {len(domains)} domains with {self.max_workers} workers")
        
        # Create thread pool
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            self.executor = executor
            
            # Submit all tasks
            future_to_domain = {
                executor.submit(self._process_single_domain, domain, i): (domain, i)
                for i, domain in enumerate(domains)
            }
            
            self.futures = list(future_to_domain.keys())
            self.processing_stats.active_threads = len(self.futures)
            
            try:
                # Process completed futures as they finish
                for future in as_completed(future_to_domain):
                    if not self.is_running:
                        break
                    
                    domain, domain_index = future_to_domain[future]
                    
                    try:
                        result = future.result()
                        
                        # Update statistics
                        self._update_statistics(result)
                        
                        # Store result
                        self.completed_results.append(result)
                        
                        # Call callbacks
                        if result_callback:
                            result_callback(result)
                        
                        if progress_callback:
                            progress_callback(
                                self.processing_stats.completed_domains,
                                self.processing_stats.total_domains,
                                self.processing_stats
                            )
                        
                        # Yield result for real-time processing
                        yield result
                        
                    except Exception as e:
                        self.logger.error(f"Error getting result for {domain}: {str(e)}")
                        self.logger.error(traceback.format_exc())
                        
                        # Create error result
                        error_result = ProcessingResult(
                            domain=domain,
                            result={
                                'domain': domain,
                                'month_col': 0,
                                'total_snapshots': 0,
                                'first_date': '',
                                'last_date': '',
                                'status_codes': {},
                                'error': f"Processing failed: {str(e)}"
                            },
                            processing_time=0.0,
                            thread_id=0,
                            timestamp=time.time()
                        )
                        
                        self._update_statistics(error_result)
                        yield error_result
            
            finally:
                self.is_running = False
                self.executor = None
                
                # Log final statistics
                total_time = time.time() - self.start_time if self.start_time else 0
                self.logger.info(
                    f"Concurrent processing completed: {self.processing_stats.completed_domains}/{self.processing_stats.total_domains} domains "
                    f"in {total_time:.2f}s (avg: {self.processing_stats.average_time_per_domain:.2f}s/domain)"
                )
    
    def pause_processing(self):
        """Pause processing"""
        self.is_paused = True
        self.pause_event.clear()
        self.logger.info("Processing paused")
    
    def resume_processing(self):
        """Resume processing"""
        self.is_paused = False
        self.pause_event.set()
        self.logger.info("Processing resumed")
    
    def stop_processing(self):
        """Stop processing"""
        self.is_running = False
        self.pause_event.set()  # Unblock any waiting threads
        
        # Cancel pending futures
        if self.futures:
            for future in self.futures:
                future.cancel()
        
        self.logger.info("Processing stopped")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive processing statistics"""
        rate_limiter_stats = self.rate_limiter.get_statistics()
        
        return {
            'processing': self.processing_stats.__dict__,
            'rate_limiter': rate_limiter_stats,
            'control': {
                'is_running': self.is_running,
                'is_paused': self.is_paused,
                'max_workers': self.max_workers
            },
            'timing': {
                'start_time': self.start_time,
                'elapsed_time': time.time() - self.start_time if self.start_time else 0,
                'processing_times': self.processing_times[-10:] if self.processing_times else []
            }
        }
    
    def get_partial_results(self) -> List[Dict[str, Any]]:
        """Get partial results for export"""
        return [result.result for result in self.completed_results]
    
    def adjust_worker_count(self, new_worker_count: int):
        """Adjust worker count (takes effect on next processing run)"""
        optimal_count = self.rate_limiter.get_optimal_thread_count()
        
        if new_worker_count > optimal_count:
            self.logger.warning(
                f"Requested {new_worker_count} workers exceeds optimal count of {optimal_count} "
                f"based on rate limits. Performance may be limited by API rate limits."
            )
        
        self.max_workers = max(1, min(new_worker_count, 10))
        self.logger.info(f"Worker count adjusted to {self.max_workers}")
