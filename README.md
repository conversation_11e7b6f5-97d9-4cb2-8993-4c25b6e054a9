# SEO Domain Analyzer - Wayback Machine

Ứng dụng web Python sử dụng Streamlit để phân tích thông tin SEO của các domain thông qua dữ liệu từ Wayback Machine.

## 🚀 Tính năng chính

### 📊 Phân tích Domain
- **Tổng số snapshot**: Đ<PERSON><PERSON> tổng số bản lưu trữ có sẵn
- **Month Col**: <PERSON><PERSON> tháng duy nhất có ít nhất 1 snapshot
- **Thời gian đầu/cuối**: Ngày của snapshot đầu tiên và cuối cùng (định dạng dd/mm/yyyy)
- **Thống kê Status Code**: Phân bố các mã HTTP response với số lượng tương ứng

### 🎯 Khả năng xử lý
- Hỗ trợ kiểm tra tối đa **5000 domain** trong một lần chạy
- Xử lý batch với progress tracking
- Auto-save kết quả tạm thời mỗi 10 domain

### 📝 Input linh hoạt
- **Text input**: <PERSON>h<PERSON><PERSON> danh sách domain (mỗi domain một dòng)
- **File upload**: Hỗ trợ .txt, .csv (cột đầu tiên chứa domain)
- **Validation**: Kiểm tra format domain hợp lệ trước khi xử lý

### 📈 Xuất dữ liệu
- **CSV format**: UTF-8 encoding, comma-separated
- **Excel format**: .xlsx với formatting cơ bản
- **Clipboard copy**: Sao chép toàn bộ bảng kết quả
- Include timestamp trong tên file export

## 🛠️ Cài đặt

### Yêu cầu hệ thống
- Python 3.8+
- Kết nối internet để truy cập Wayback Machine API

### Cài đặt dependencies
```bash
pip install -r requirements.txt
```

### Chạy ứng dụng
```bash
streamlit run app.py
```

Ứng dụng sẽ mở tại: `http://localhost:8501`

## 📋 Hướng dẫn sử dụng

### 1. Nhập danh sách Domain
- **Cách 1**: Nhập trực tiếp vào text area, mỗi domain một dòng
- **Cách 2**: Upload file .txt hoặc .csv

### 2. Validation
- Ứng dụng tự động kiểm tra và làm sạch domain
- Loại bỏ domain trùng lặp và không hợp lệ
- Hiển thị số lượng domain hợp lệ tìm thấy

### 3. Phân tích
- Click "🚀 Bắt đầu phân tích"
- Theo dõi tiến độ qua progress bar
- Xem thời gian ước tính hoàn thành

### 4. Xem kết quả
- Bảng kết quả với các cột:
  - **STT**: Số thứ tự tự động
  - **Domain**: Tên domain
  - **Month Col**: Số tháng có snapshot
  - **Snapshot**: Tổng số snapshot
  - **Thời gian**: Format dd/mm/yyyy - dd/mm/yyyy
  - **StatusCode**: Format 200:5; 301:2 với màu sắc phân biệt

### 5. Xuất dữ liệu
- **Download CSV**: File CSV với encoding UTF-8
- **Download Excel**: File .xlsx với formatting
- **Copy to Clipboard**: Sao chép toàn bộ dữ liệu

## 🎨 Màu sắc Status Code

- 🟢 **2xx**: Thành công (Success)
- 🟡 **3xx**: Chuyển hướng (Redirect)
- 🔴 **4xx**: Lỗi client (Client Error)
- 🔴 **5xx**: Lỗi server (Server Error)

## ⚙️ Cấu hình kỹ thuật

### Rate Limiting
Ứng dụng tuân thủ nghiêm ngặt rate limits của Wayback Machine API:
- **search_calls_per_second**: 0.8 calls/s
- **memento_calls_per_second**: 8 calls/s
- **timemap_calls_per_second**: 1.33 calls/s

### Error Handling
- Exponential backoff khi gặp 429 error
- Retry logic với delay tăng dần
- Circuit breaker pattern cho API calls
- Log lỗi chi tiết cho debugging

### Performance
- Batch processing để tránh timeout
- Session state để lưu trạng thái pause/resume
- Auto-save kết quả tạm thời
- Memory-efficient data processing

## 📁 Cấu trúc dự án

```
├── app.py                 # Streamlit main application
├── wayback_analyzer.py    # Core logic tương tác Wayback Machine
├── data_processor.py      # Utilities xử lý và validation dữ liệu
├── error_handler.py       # Enhanced error handling và rate limiting
├── test_app.py           # Test suite
├── requirements.txt      # Python dependencies
└── README.md            # Documentation
```

## 🧪 Testing

Chạy test suite để kiểm tra functionality:

```bash
python test_app.py
```

Test suite bao gồm:
- Domain validation tests
- Data processing tests
- Error handling tests
- Integration tests
- Performance tests (optional)

## 📊 Logic tính toán Month Col

```
1. Lấy tất cả timestamp từ Wayback Machine API
2. Chuyển đổi timestamp thành định dạng YYYY-MM
3. Loại bỏ trùng lặp để có danh sách các tháng duy nhất
4. Đếm số lượng tháng duy nhất
5. Ghi kết quả vào cột Month Col

Ví dụ:
- Domain: example.com có 12 snapshots
- 6 snapshots vào 2024-12
- 3 snapshots vào 2025-01  
- 3 snapshots vào 2025-02
- Kết quả Month Col = 3 (có snapshot trong 3 tháng khác nhau)
```

## ⚠️ Lưu ý quan trọng

### Rate Limiting
- Ứng dụng tự động tuân thủ rate limits của Wayback Machine
- Không nên chạy multiple instances cùng lúc
- Thời gian xử lý phụ thuộc vào số lượng domain và rate limits

### Network Requirements
- Cần kết nối internet ổn định
- Firewall có thể cần cấu hình cho web.archive.org
- Proxy settings có thể ảnh hưởng đến kết nối

### Data Accuracy
- Dữ liệu phụ thuộc vào tính khả dụng của Wayback Machine
- Một số domain có thể không có snapshot
- Kết quả có thể thay đổi theo thời gian

## 🐛 Troubleshooting

### Lỗi kết nối
```
Error: No connection could be made
```
**Giải pháp**: Kiểm tra kết nối internet và firewall settings

### Lỗi rate limit
```
Error: 429 Too Many Requests
```
**Giải pháp**: Ứng dụng tự động retry với exponential backoff

### Lỗi memory
```
Error: Out of memory
```
**Giải pháp**: Giảm số lượng domain hoặc tăng RAM

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra logs trong terminal
2. Chạy test suite để xác định vấn đề
3. Kiểm tra kết nối internet
4. Xem error statistics trong ứng dụng

## 📄 License

MIT License - Xem file LICENSE để biết thêm chi tiết.

## 🙏 Acknowledgments

- [Wayback Machine](https://web.archive.org/) - Internet Archive
- [waybackpy](https://github.com/akamhy/waybackpy) - Python library
- [Streamlit](https://streamlit.io/) - Web app framework
