"""
Wayback Machine Analyzer - Core logic for SEO domain analysis
"""

import time
import logging
from datetime import datetime
from typing import List, Dict, Optional, Tuple, Set
from collections import defaultdict, Counter
import validators
import backoff
from waybackpy import WaybackMachineCDXServerAPI
from waybackpy.exceptions import WaybackError
import requests.exceptions
from error_handler import WaybackError<PERSON>and<PERSON>, with_error_handling
from network_utils import check_wayback_connectivity, log_network_error_smart


class WaybackAnalyzer:
    """Core class for analyzing domains using Wayback Machine data"""
    
    def __init__(self, user_agent: str = "SEO-Domain-Analyzer/1.0"):
        self.user_agent = user_agent
        self.logger = self._setup_logger()

        # Initialize enhanced error handler
        self.error_handler = WaybackErrorHandler(self.logger)

        # Network connectivity tracking
        self.connectivity_checked = False
        self.network_available = True
        self.consecutive_network_failures = 0
        self.max_network_failures = 5  # Circuit breaker threshold

        # Legacy rate limiting (now handled by error_handler)
        self.search_calls_per_second = 0.8
        self.memento_calls_per_second = 8.0
        self.timemap_calls_per_second = 1.33

        # Tracking for rate limiting
        self.last_call_time = 0
        self.call_count = 0
        
    def _setup_logger(self) -> logging.Logger:
        """Setup logging for the analyzer"""
        logger = logging.getLogger('wayback_analyzer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def check_network_connectivity(self) -> bool:
        """Check network connectivity to Wayback Machine"""
        if not self.connectivity_checked:
            self.network_available = check_wayback_connectivity()
            self.connectivity_checked = True

            if not self.network_available:
                self.logger.error("❌ Cannot connect to Wayback Machine. Please check your internet connection and firewall settings.")
            else:
                self.logger.info("✅ Wayback Machine connectivity verified")

        return self.network_available

    def validate_domain(self, domain: str) -> bool:
        """Validate if domain format is correct"""
        # Remove protocol if present
        domain = domain.replace('http://', '').replace('https://', '')
        domain = domain.replace('www.', '')
        domain = domain.strip('/')

        # Basic validation
        if not domain or '.' not in domain:
            return False

        # Use validators library for more robust validation
        try:
            return validators.domain(domain) is True
        except:
            return False
    
    def _rate_limit_delay(self):
        """Legacy rate limiting - now handled by error_handler"""
        # This method is kept for backward compatibility
        # Rate limiting is now handled by the error_handler's rate_limiter
        pass
    
    @with_error_handling(max_attempts=5, api_type='search')
    def _get_snapshots_with_retry(self, domain: str) -> List:
        """Get snapshots with enhanced error handling and rate limiting"""
        # Circuit breaker: if too many consecutive network failures, skip
        if self.consecutive_network_failures >= self.max_network_failures:
            raise Exception(f"Network circuit breaker activated. Skipping {domain} due to repeated connectivity issues.")

        try:
            # Ensure domain has protocol
            if not domain.startswith(('http://', 'https://')):
                url = f"https://{domain}"
            else:
                url = domain

            self.logger.info(f"Fetching snapshots for: {url}")

            # Initialize CDX API
            cdx_api = WaybackMachineCDXServerAPI(
                url=url,
                user_agent=self.user_agent
            )

            # Get all snapshots
            snapshots = list(cdx_api.snapshots())
            self.logger.info(f"Found {len(snapshots)} snapshots for {domain}")

            # Reset network failure counter on success
            self.consecutive_network_failures = 0

            return snapshots

        except requests.exceptions.ConnectionError as e:
            # Handle network connectivity issues specifically
            self.consecutive_network_failures += 1
            log_network_error_smart(domain, e)

            if self.consecutive_network_failures >= self.max_network_failures:
                self.logger.error(f"❌ Network circuit breaker activated after {self.max_network_failures} consecutive failures")

            raise Exception(f"Network connectivity issue for {domain}")

        except Exception as e:
            # Handle other errors normally
            self.logger.error(f"Error fetching snapshots for {domain}: {str(e)}")
            raise
    
    def calculate_month_col(self, snapshots: List) -> int:
        """Calculate Month Col - number of unique months with snapshots"""
        if not snapshots:
            return 0
            
        unique_months = set()
        
        for snapshot in snapshots:
            try:
                # Extract timestamp and convert to YYYY-MM format
                timestamp = snapshot.timestamp
                if len(timestamp) >= 6:
                    year_month = f"{timestamp[:4]}-{timestamp[4:6]}"
                    unique_months.add(year_month)
            except Exception as e:
                self.logger.warning(f"Error processing timestamp: {e}")
                continue
                
        return len(unique_months)
    
    def get_time_range(self, snapshots: List) -> Tuple[str, str]:
        """Get first and last snapshot dates in dd/mm/yyyy format"""
        if not snapshots:
            return "", ""
            
        try:
            timestamps = []
            for snapshot in snapshots:
                try:
                    timestamps.append(snapshot.timestamp)
                except:
                    continue
                    
            if not timestamps:
                return "", ""
                
            timestamps.sort()
            
            # Convert first and last timestamps to dd/mm/yyyy
            first_date = self._timestamp_to_date(timestamps[0])
            last_date = self._timestamp_to_date(timestamps[-1])
            
            return first_date, last_date
            
        except Exception as e:
            self.logger.error(f"Error calculating time range: {e}")
            return "", ""
    
    def _timestamp_to_date(self, timestamp: str) -> str:
        """Convert wayback timestamp to dd/mm/yyyy format"""
        try:
            if len(timestamp) >= 8:
                year = timestamp[:4]
                month = timestamp[4:6]
                day = timestamp[6:8]
                return f"{day}/{month}/{year}"
        except:
            pass
        return ""
    
    def get_status_code_stats(self, snapshots: List) -> Dict[str, int]:
        """Get status code statistics"""
        status_counts = Counter()
        
        for snapshot in snapshots:
            try:
                status_code = snapshot.statuscode
                if status_code:
                    status_counts[str(status_code)] += 1
            except Exception as e:
                self.logger.warning(f"Error processing status code: {e}")
                continue
                
        return dict(status_counts)
    
    def analyze_domain(self, domain: str) -> Dict:
        """Analyze a single domain and return comprehensive data"""
        result = {
            'domain': domain,
            'month_col': 0,
            'total_snapshots': 0,
            'first_date': '',
            'last_date': '',
            'status_codes': {},
            'error': None
        }

        try:
            # Validate domain
            if not self.validate_domain(domain):
                result['error'] = 'Invalid domain format'
                return result

            # Check network connectivity before proceeding
            if not self.check_network_connectivity():
                result['error'] = 'Network connectivity issue - cannot reach Wayback Machine'
                return result

            # Skip if circuit breaker is active
            if self.consecutive_network_failures >= self.max_network_failures:
                result['error'] = f'Network circuit breaker active - skipping due to repeated connectivity issues'
                return result

            # Get snapshots
            snapshots = self._get_snapshots_with_retry(domain)

            if not snapshots:
                result['error'] = 'No snapshots found'
                return result

            # Calculate metrics
            result['total_snapshots'] = len(snapshots)
            result['month_col'] = self.calculate_month_col(snapshots)

            first_date, last_date = self.get_time_range(snapshots)
            result['first_date'] = first_date
            result['last_date'] = last_date

            result['status_codes'] = self.get_status_code_stats(snapshots)

            self.logger.info(f"Successfully analyzed {domain}")

        except Exception as e:
            # Check if it's a network-related error
            if "Network connectivity issue" in str(e) or "circuit breaker" in str(e):
                result['error'] = str(e)
            else:
                error_msg = f"Analysis failed: {str(e)}"
                result['error'] = error_msg
                self.logger.error(f"Error analyzing {domain}: {error_msg}")

        return result
    
    def analyze_domains_batch(self, domains: List[str], progress_callback=None) -> List[Dict]:
        """Analyze multiple domains with progress tracking"""
        results = []
        total_domains = len(domains)

        self.logger.info(f"Starting batch analysis of {total_domains} domains")

        # Initial connectivity check
        if not self.check_network_connectivity():
            self.logger.error("❌ Cannot proceed with batch analysis - no network connectivity to Wayback Machine")
            # Return all domains with network error
            for domain in domains:
                results.append({
                    'domain': domain.strip(),
                    'month_col': 0,
                    'total_snapshots': 0,
                    'first_date': '',
                    'last_date': '',
                    'status_codes': {},
                    'error': 'Network connectivity issue - cannot reach Wayback Machine'
                })
            return results

        for i, domain in enumerate(domains):
            try:
                result = self.analyze_domain(domain.strip())
                results.append(result)

                # Progress callback
                if progress_callback:
                    progress = (i + 1) / total_domains
                    progress_callback(progress, i + 1, total_domains)

                # If circuit breaker is active, skip remaining domains
                if self.consecutive_network_failures >= self.max_network_failures:
                    self.logger.warning(f"⚠️ Network circuit breaker active. Skipping remaining {total_domains - i - 1} domains")
                    # Add remaining domains with circuit breaker error
                    for remaining_domain in domains[i+1:]:
                        results.append({
                            'domain': remaining_domain.strip(),
                            'month_col': 0,
                            'total_snapshots': 0,
                            'first_date': '',
                            'last_date': '',
                            'status_codes': {},
                            'error': 'Skipped due to network circuit breaker activation'
                        })
                    break

            except Exception as e:
                self.logger.error(f"Batch processing error for {domain}: {e}")
                results.append({
                    'domain': domain,
                    'month_col': 0,
                    'total_snapshots': 0,
                    'first_date': '',
                    'last_date': '',
                    'status_codes': {},
                    'error': f"Processing failed: {str(e)}"
                })

        self.logger.info(f"Completed batch analysis. Processed {len(results)} domains")

        # Log error statistics
        error_stats = self.error_handler.get_error_stats()
        self.logger.info(f"Error statistics: {error_stats}")

        return results

    def get_error_statistics(self) -> Dict:
        """Get comprehensive error statistics"""
        return self.error_handler.get_error_stats()
