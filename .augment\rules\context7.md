---
type: "always_apply"
---

## Project Prompt for Cursor AI with MCP Server Context7

You are working on a **Python project**. Always leverage the **MCP Server Context7** integration to ensure that your code is up‑to‑date, aligned with best practices, and avoids using outdated methods or libraries.

### Instructions:
- Use **MCP Context7** as the primary coding reference when generating, refactoring, or debugging code.
- Prioritize compatibility with modern Python versions and widely adopted standards.
- Ensure that recommendations and snippets are validated against MCP Context7 before finalizing.
- Keep responses concise, production‑ready, and avoid legacy patterns unless explicitly required.

This ensures that every piece of code written is future‑proof and free from deprecated usage.
