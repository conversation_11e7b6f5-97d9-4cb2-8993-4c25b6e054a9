"""
Thread-safe rate limiter for concurrent Wayback Machine API calls
"""

import time
import threading
import logging
from typing import Optional, Dict, Any
from dataclasses import dataclass
from collections import deque


@dataclass
class RateLimitConfig:
    """Configuration for rate limiting"""
    calls_per_second: float = 0.8
    burst_capacity: int = 3
    safety_factor: float = 0.8
    
    @property
    def effective_rate(self) -> float:
        """Effective rate with safety factor applied"""
        return self.calls_per_second * self.safety_factor


class ThreadSafeRateLimiter:
    """Thread-safe rate limiter using token bucket algorithm"""
    
    def __init__(self, config: RateLimitConfig, logger: Optional[logging.Logger] = None):
        self.config = config
        self.logger = logger or self._setup_logger()
        
        # Token bucket parameters
        self.bucket_capacity = config.burst_capacity
        self.tokens = float(self.bucket_capacity)
        self.last_refill = time.time()
        
        # Thread synchronization
        self._lock = threading.RLock()
        self._condition = threading.Condition(self._lock)
        
        # Statistics tracking
        self.total_requests = 0
        self.total_waits = 0
        self.total_wait_time = 0.0
        self.request_times = deque(maxlen=100)  # Last 100 request times
        
        # Active threads tracking
        self.active_threads = set()
        self.thread_stats = {}
        
    def _setup_logger(self) -> logging.Logger:
        """Setup logger for rate limiter"""
        logger = logging.getLogger('thread_safe_rate_limiter')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _refill_tokens(self):
        """Refill tokens based on elapsed time"""
        now = time.time()
        elapsed = now - self.last_refill
        
        # Add tokens based on elapsed time and rate
        tokens_to_add = elapsed * self.config.effective_rate
        self.tokens = min(self.bucket_capacity, self.tokens + tokens_to_add)
        self.last_refill = now
    
    def acquire(self, tokens: int = 1, timeout: Optional[float] = None) -> bool:
        """
        Acquire tokens from the bucket
        
        Args:
            tokens: Number of tokens to acquire
            timeout: Maximum time to wait for tokens (None = wait indefinitely)
            
        Returns:
            True if tokens acquired, False if timeout
        """
        thread_id = threading.get_ident()
        start_time = time.time()
        
        with self._condition:
            # Register thread
            self.active_threads.add(thread_id)
            if thread_id not in self.thread_stats:
                self.thread_stats[thread_id] = {
                    'requests': 0,
                    'waits': 0,
                    'total_wait_time': 0.0
                }
            
            while True:
                self._refill_tokens()
                
                if self.tokens >= tokens:
                    # Sufficient tokens available
                    self.tokens -= tokens
                    
                    # Update statistics
                    self.total_requests += 1
                    self.thread_stats[thread_id]['requests'] += 1
                    self.request_times.append(time.time())
                    
                    wait_time = time.time() - start_time
                    if wait_time > 0.001:  # Only count significant waits
                        self.total_waits += 1
                        self.total_wait_time += wait_time
                        self.thread_stats[thread_id]['waits'] += 1
                        self.thread_stats[thread_id]['total_wait_time'] += wait_time
                    
                    return True
                
                # Not enough tokens, need to wait
                if timeout is not None:
                    elapsed = time.time() - start_time
                    if elapsed >= timeout:
                        return False
                    remaining_timeout = timeout - elapsed
                else:
                    remaining_timeout = None
                
                # Calculate wait time for next token
                tokens_needed = tokens - self.tokens
                wait_time = tokens_needed / self.config.effective_rate
                
                # Wait for tokens to be available
                self._condition.wait(min(wait_time, remaining_timeout) if remaining_timeout else wait_time)
    
    def release_thread(self, thread_id: Optional[int] = None):
        """Release thread from tracking"""
        if thread_id is None:
            thread_id = threading.get_ident()
        
        with self._lock:
            self.active_threads.discard(thread_id)
    
    def get_current_rate(self) -> float:
        """Get current actual request rate"""
        with self._lock:
            if len(self.request_times) < 2:
                return 0.0
            
            # Calculate rate over last 60 seconds
            now = time.time()
            recent_requests = [t for t in self.request_times if now - t <= 60]
            
            if len(recent_requests) < 2:
                return 0.0
            
            time_span = recent_requests[-1] - recent_requests[0]
            if time_span <= 0:
                return 0.0
            
            return (len(recent_requests) - 1) / time_span
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive rate limiter statistics"""
        with self._lock:
            current_rate = self.get_current_rate()
            
            stats = {
                'config': {
                    'calls_per_second': self.config.calls_per_second,
                    'effective_rate': self.config.effective_rate,
                    'burst_capacity': self.bucket_capacity,
                    'safety_factor': self.config.safety_factor
                },
                'current_state': {
                    'available_tokens': self.tokens,
                    'current_rate': current_rate,
                    'rate_utilization': current_rate / self.config.effective_rate if self.config.effective_rate > 0 else 0
                },
                'totals': {
                    'total_requests': self.total_requests,
                    'total_waits': self.total_waits,
                    'total_wait_time': self.total_wait_time,
                    'average_wait_time': self.total_wait_time / max(self.total_waits, 1)
                },
                'threads': {
                    'active_count': len(self.active_threads),
                    'active_threads': list(self.active_threads),
                    'thread_stats': self.thread_stats.copy()
                }
            }
            
            return stats
    
    def reset_statistics(self):
        """Reset all statistics"""
        with self._lock:
            self.total_requests = 0
            self.total_waits = 0
            self.total_wait_time = 0.0
            self.request_times.clear()
            self.thread_stats.clear()
    
    def adjust_rate(self, new_rate: float):
        """Dynamically adjust the rate limit"""
        with self._lock:
            old_rate = self.config.calls_per_second
            self.config.calls_per_second = new_rate
            self.logger.info(f"Rate limit adjusted from {old_rate} to {new_rate} calls/s")
            
            # Notify waiting threads
            self._condition.notify_all()
    
    def get_optimal_thread_count(self) -> int:
        """Calculate optimal thread count based on rate limits"""
        # Each domain typically requires 1-2 API calls
        # With safety factor, we want to stay well under the limit
        calls_per_domain = 1.5  # Average estimate
        max_concurrent_domains = self.config.effective_rate / calls_per_domain
        
        # Round down and ensure minimum of 1
        optimal_threads = max(1, int(max_concurrent_domains))
        
        # Cap at reasonable maximum
        return min(optimal_threads, 5)
    
    def wait_for_capacity(self, required_tokens: int = 1, timeout: float = 30.0) -> bool:
        """Wait until sufficient capacity is available"""
        return self.acquire(required_tokens, timeout)


class AdaptiveRateLimiter(ThreadSafeRateLimiter):
    """Rate limiter that adapts based on API responses"""
    
    def __init__(self, config: RateLimitConfig, logger: Optional[logging.Logger] = None):
        super().__init__(config, logger)
        self.consecutive_rate_limit_errors = 0
        self.last_rate_limit_time = 0
        self.adaptation_factor = 0.1
    
    def report_rate_limit_error(self):
        """Report that a rate limit error occurred"""
        with self._lock:
            self.consecutive_rate_limit_errors += 1
            self.last_rate_limit_time = time.time()
            
            # Reduce rate if getting rate limit errors
            if self.consecutive_rate_limit_errors >= 2:
                reduction_factor = 1 - (self.adaptation_factor * self.consecutive_rate_limit_errors)
                new_rate = self.config.calls_per_second * max(0.1, reduction_factor)
                
                self.logger.warning(
                    f"Rate limit errors detected. Reducing rate from "
                    f"{self.config.calls_per_second:.2f} to {new_rate:.2f} calls/s"
                )
                
                self.adjust_rate(new_rate)
    
    def report_success(self):
        """Report successful API call"""
        with self._lock:
            # Gradually increase rate if no recent errors
            if (self.consecutive_rate_limit_errors > 0 and 
                time.time() - self.last_rate_limit_time > 60):
                
                self.consecutive_rate_limit_errors = max(0, self.consecutive_rate_limit_errors - 1)
                
                if self.consecutive_rate_limit_errors == 0:
                    # Gradually restore original rate
                    original_rate = 0.8  # Original Wayback Machine rate
                    if self.config.calls_per_second < original_rate:
                        new_rate = min(original_rate, self.config.calls_per_second * 1.1)
                        self.adjust_rate(new_rate)


# Global rate limiter instance
_global_rate_limiter = None
_rate_limiter_lock = threading.Lock()


def get_global_rate_limiter() -> AdaptiveRateLimiter:
    """Get or create global rate limiter instance"""
    global _global_rate_limiter
    
    if _global_rate_limiter is None:
        with _rate_limiter_lock:
            if _global_rate_limiter is None:
                config = RateLimitConfig()
                _global_rate_limiter = AdaptiveRateLimiter(config)
    
    return _global_rate_limiter


def reset_global_rate_limiter():
    """Reset global rate limiter"""
    global _global_rate_limiter
    
    with _rate_limiter_lock:
        if _global_rate_limiter:
            _global_rate_limiter.reset_statistics()
        _global_rate_limiter = None
