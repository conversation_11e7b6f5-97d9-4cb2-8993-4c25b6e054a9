"""
Test script for SEO Domain Analyzer
"""

import sys
import time
from wayback_analyzer import WaybackAnalyzer
from data_processor import DataProcessor
from error_handler import get_error_handler


def test_domain_validation():
    """Test domain validation functionality"""
    print("🧪 Testing domain validation...")
    
    test_domains = [
        "google.com",
        "https://facebook.com",
        "www.github.com",
        "invalid-domain",
        "test..com",
        "example.org",
        "",
        "sub.domain.example.com"
    ]
    
    processor = DataProcessor()
    
    for domain in test_domains:
        is_valid = processor.validate_domain_format(domain)
        cleaned = processor.clean_domain(domain)
        print(f"  {domain:25} -> Valid: {is_valid:5} | Cleaned: {cleaned}")
    
    print("✅ Domain validation test completed\n")


def test_wayback_analyzer():
    """Test Wayback analyzer with sample domains"""
    print("🧪 Testing Wayback analyzer...")
    
    analyzer = WaybackAnalyzer()
    
    # Test with a few reliable domains
    test_domains = [
        "example.com",
        "google.com"
    ]
    
    print(f"Testing with domains: {test_domains}")
    
    for domain in test_domains:
        print(f"\n📊 Analyzing {domain}...")
        try:
            result = analyzer.analyze_domain(domain)
            
            print(f"  Domain: {result['domain']}")
            print(f"  Month Col: {result['month_col']}")
            print(f"  Total Snapshots: {result['total_snapshots']}")
            print(f"  Time Range: {result['first_date']} - {result['last_date']}")
            print(f"  Status Codes: {result['status_codes']}")
            
            if result['error']:
                print(f"  ❌ Error: {result['error']}")
            else:
                print(f"  ✅ Success")
                
        except Exception as e:
            print(f"  ❌ Exception: {str(e)}")
        
        # Small delay between requests
        time.sleep(2)
    
    # Get error statistics
    error_stats = analyzer.get_error_statistics()
    print(f"\n📈 Error Statistics: {error_stats}")
    
    print("✅ Wayback analyzer test completed\n")


def test_data_processing():
    """Test data processing functionality"""
    print("🧪 Testing data processing...")
    
    # Test text extraction
    sample_text = """
    google.com
    https://facebook.com
    www.github.com
    invalid-domain
    example.org
    """
    
    domains = DataProcessor.extract_domains_from_text(sample_text)
    print(f"Extracted domains from text: {domains}")
    
    # Test CSV data simulation
    csv_content = b"Domain,Other\ngoogle.com,test\nfacebook.com,test2\ninvalid,test3"
    try:
        csv_domains = DataProcessor.extract_domains_from_csv(csv_content)
        print(f"Extracted domains from CSV: {csv_domains}")
    except Exception as e:
        print(f"CSV extraction error: {e}")
    
    # Test timestamp formatting
    test_timestamps = ["20240315123045", "20231201000000", "invalid"]
    for ts in test_timestamps:
        formatted = DataProcessor.format_timestamp_to_date(ts)
        print(f"Timestamp {ts} -> {formatted}")
    
    # Test month calculation
    timestamps = ["20240315123045", "20240315140000", "20240401120000", "20240501100000"]
    months = DataProcessor.calculate_unique_months(timestamps)
    print(f"Unique months from {len(timestamps)} timestamps: {months}")
    
    # Test status code formatting
    status_codes = {"200": 10, "301": 3, "404": 1, "500": 2}
    formatted_status = DataProcessor.format_status_codes_display(status_codes)
    print(f"Formatted status codes: {formatted_status}")
    
    print("✅ Data processing test completed\n")


def test_error_handler():
    """Test error handling functionality"""
    print("🧪 Testing error handler...")
    
    error_handler = get_error_handler()
    
    # Test error classification
    test_errors = [
        Exception("Rate limit exceeded"),
        Exception("Connection timeout"),
        Exception("Network error"),
        Exception("429 Too Many Requests"),
        Exception("Unknown error")
    ]
    
    for error in test_errors:
        error_type = error_handler.classify_error(error)
        should_retry = error_handler.should_retry(error, 1, 5)
        print(f"Error: {str(error)[:30]:30} -> Type: {error_type:12} | Retry: {should_retry}")
    
    # Test rate limiter
    print("\n⏱️  Testing rate limiter...")
    start_time = time.time()
    
    for i in range(3):
        error_handler.rate_limiter.wait_for_rate_limit('search')
        print(f"  Call {i+1} completed")
    
    elapsed = time.time() - start_time
    print(f"  3 calls took {elapsed:.2f} seconds")
    
    # Get rate limiter stats
    stats = error_handler.rate_limiter.get_stats()
    print(f"  Rate limiter stats: {stats}")
    
    print("✅ Error handler test completed\n")


def test_integration():
    """Test integration with small batch"""
    print("🧪 Testing integration with small batch...")
    
    analyzer = WaybackAnalyzer()
    
    # Small test batch
    test_domains = ["example.com", "httpbin.org"]
    
    print(f"Processing batch of {len(test_domains)} domains...")
    
    def progress_callback(progress, current, total):
        print(f"  Progress: {current}/{total} ({progress*100:.1f}%)")
    
    try:
        results = analyzer.analyze_domains_batch(test_domains, progress_callback)
        
        print(f"\n📊 Batch Results:")
        for result in results:
            print(f"  {result['domain']:15} | Snapshots: {result['total_snapshots']:4} | Months: {result['month_col']:2} | Error: {result.get('error', 'None')}")
        
        # Test data export
        print(f"\n💾 Testing export functionality...")
        
        csv_data = DataProcessor.export_to_csv(results)
        print(f"  CSV export: {len(csv_data)} characters")
        
        excel_data = DataProcessor.export_to_excel(results)
        print(f"  Excel export: {len(excel_data)} bytes")
        
        # Test summary stats
        stats = DataProcessor.create_summary_stats(results)
        print(f"  Summary stats: {stats}")
        
    except Exception as e:
        print(f"❌ Integration test failed: {str(e)}")
    
    print("✅ Integration test completed\n")


def run_performance_test():
    """Test performance with larger dataset"""
    print("🚀 Running performance test...")
    
    # Generate test domains
    test_domains = [
        f"test{i}.example.com" for i in range(10)
    ] + [
        "google.com",
        "github.com", 
        "stackoverflow.com"
    ]
    
    print(f"Testing with {len(test_domains)} domains...")
    
    analyzer = WaybackAnalyzer()
    
    start_time = time.time()
    
    def progress_callback(progress, current, total):
        elapsed = time.time() - start_time
        if current > 0:
            eta = (elapsed / current) * (total - current)
            print(f"  Progress: {current}/{total} ({progress*100:.1f}%) | Elapsed: {elapsed:.1f}s | ETA: {eta:.1f}s")
    
    try:
        results = analyzer.analyze_domains_batch(test_domains, progress_callback)
        
        total_time = time.time() - start_time
        successful = sum(1 for r in results if not r.get('error'))
        
        print(f"\n📈 Performance Results:")
        print(f"  Total time: {total_time:.2f} seconds")
        print(f"  Domains processed: {len(results)}")
        print(f"  Successful: {successful}")
        print(f"  Failed: {len(results) - successful}")
        print(f"  Average time per domain: {total_time/len(results):.2f}s")
        
        # Error statistics
        error_stats = analyzer.get_error_statistics()
        print(f"  Error statistics: {error_stats}")
        
    except Exception as e:
        print(f"❌ Performance test failed: {str(e)}")
    
    print("✅ Performance test completed\n")


def main():
    """Run all tests"""
    print("🔬 SEO Domain Analyzer - Test Suite")
    print("=" * 50)
    
    try:
        # Basic functionality tests
        test_domain_validation()
        test_data_processing()
        test_error_handler()
        
        # Integration tests
        test_integration()
        
        # Wayback API tests (may take longer)
        print("⚠️  The following tests will make real API calls and may take time...")
        user_input = input("Continue with API tests? (y/N): ").strip().lower()
        
        if user_input == 'y':
            test_wayback_analyzer()
            run_performance_test()
        else:
            print("⏭️  Skipping API tests")
        
        print("🎉 All tests completed!")
        
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
