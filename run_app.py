"""
Launcher script for SEO Domain Analyzer
"""

import subprocess
import sys
import os
from pathlib import Path


def check_requirements():
    """Check if all required packages are installed"""
    try:
        import streamlit
        import waybackpy
        import pandas
        import openpyxl
        import pyperclip
        import validators
        import backoff
        import tenacity
        import arrow
        print("✅ All required packages are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing package: {e}")
        return False


def install_requirements():
    """Install requirements from requirements.txt"""
    print("📦 Installing requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False


def run_streamlit():
    """Run the Streamlit application"""
    print("🚀 Starting SEO Domain Analyzer...")
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "app.py"])
    except KeyboardInterrupt:
        print("\n⏹️ Application stopped by user")
    except Exception as e:
        print(f"❌ Error running application: {e}")


def main():
    """Main launcher function"""
    print("🔍 SEO Domain Analyzer - Wayback Machine")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("app.py").exists():
        print("❌ app.py not found. Please run this script from the project directory.")
        return
    
    # Check requirements
    if not check_requirements():
        print("\n📦 Some packages are missing. Installing...")
        if not install_requirements():
            print("❌ Failed to install requirements. Please install manually:")
            print("   pip install -r requirements.txt")
            return
        
        # Check again after installation
        if not check_requirements():
            print("❌ Installation failed. Please check your Python environment.")
            return
    
    print("\n🌐 The application will open in your default web browser")
    print("📍 URL: http://localhost:8501")
    print("⏹️  Press Ctrl+C to stop the application")
    print("-" * 50)
    
    # Run the application
    run_streamlit()


if __name__ == "__main__":
    main()
