"""
Data Processing utilities for SEO Domain Analysis
"""

import re
import csv
import json
import pandas as pd
from datetime import datetime
from typing import List, Dict, Tuple, Optional
import validators
from urllib.parse import urlparse


class DataProcessor:
    """Utility class for data processing and validation"""
    
    @staticmethod
    def clean_domain(domain: str) -> str:
        """Clean and normalize domain string"""
        if not domain:
            return ""
        
        # Remove whitespace
        domain = domain.strip()
        
        # Remove protocol
        domain = re.sub(r'^https?://', '', domain)
        
        # Remove www prefix
        domain = re.sub(r'^www\.', '', domain)
        
        # Remove trailing slash and path
        domain = domain.split('/')[0]
        
        # Remove port if present
        domain = domain.split(':')[0]
        
        # Convert to lowercase
        domain = domain.lower()
        
        return domain
    
    @staticmethod
    def validate_domain_format(domain: str) -> bool:
        """Validate domain format using multiple methods"""
        if not domain:
            return False
        
        # Clean domain first
        clean_domain = DataProcessor.clean_domain(domain)
        
        # Basic checks
        if not clean_domain or '.' not in clean_domain:
            return False
        
        # Check for invalid characters
        if re.search(r'[^a-zA-Z0-9.-]', clean_domain):
            return False
        
        # Check for consecutive dots
        if '..' in clean_domain:
            return False
        
        # Check if starts or ends with dot or hyphen
        if clean_domain.startswith('.') or clean_domain.endswith('.'):
            return False
        if clean_domain.startswith('-') or clean_domain.endswith('-'):
            return False
        
        # Use validators library
        try:
            return validators.domain(clean_domain) is True
        except:
            return False
    
    @staticmethod
    def extract_domains_from_text(text: str) -> List[str]:
        """Extract valid domains from text input"""
        if not text:
            return []
        
        domains = []
        lines = text.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Try to extract domain from line (might contain other text)
            # Look for domain patterns
            domain_pattern = r'(?:https?://)?(?:www\.)?([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'
            matches = re.findall(domain_pattern, line)
            
            if matches:
                for match in matches:
                    clean_domain = DataProcessor.clean_domain(match)
                    if DataProcessor.validate_domain_format(clean_domain):
                        if clean_domain not in domains:
                            domains.append(clean_domain)
            else:
                # Try the whole line as domain
                clean_domain = DataProcessor.clean_domain(line)
                if DataProcessor.validate_domain_format(clean_domain):
                    if clean_domain not in domains:
                        domains.append(clean_domain)
        
        return domains
    
    @staticmethod
    def extract_domains_from_csv(file_content: bytes) -> List[str]:
        """Extract domains from CSV file content"""
        domains = []
        
        try:
            # Decode content
            content = file_content.decode('utf-8')
            
            # Parse CSV
            csv_reader = csv.reader(content.splitlines())
            
            for row_num, row in enumerate(csv_reader):
                if not row:
                    continue
                
                # Skip header row if it looks like a header
                if row_num == 0:
                    first_cell = row[0].lower().strip()
                    if first_cell in ['domain', 'url', 'website', 'site', 'domains']:
                        continue
                
                # Use first column as domain
                potential_domain = row[0].strip()
                clean_domain = DataProcessor.clean_domain(potential_domain)
                
                if DataProcessor.validate_domain_format(clean_domain):
                    if clean_domain not in domains:
                        domains.append(clean_domain)
        
        except Exception as e:
            raise ValueError(f"Error parsing CSV: {str(e)}")
        
        return domains
    
    @staticmethod
    def format_timestamp_to_date(timestamp: str) -> str:
        """Convert Wayback timestamp to dd/mm/yyyy format"""
        try:
            if len(timestamp) >= 8:
                year = timestamp[:4]
                month = timestamp[4:6]
                day = timestamp[6:8]
                
                # Validate date components
                if (1900 <= int(year) <= 2100 and 
                    1 <= int(month) <= 12 and 
                    1 <= int(day) <= 31):
                    return f"{day}/{month}/{year}"
        except (ValueError, IndexError):
            pass
        
        return ""
    
    @staticmethod
    def calculate_unique_months(timestamps: List[str]) -> int:
        """Calculate number of unique months from timestamps"""
        unique_months = set()
        
        for timestamp in timestamps:
            try:
                if len(timestamp) >= 6:
                    year_month = f"{timestamp[:4]}-{timestamp[4:6]}"
                    # Validate year and month
                    year = int(timestamp[:4])
                    month = int(timestamp[4:6])
                    
                    if 1900 <= year <= 2100 and 1 <= month <= 12:
                        unique_months.add(year_month)
            except (ValueError, IndexError):
                continue
        
        return len(unique_months)
    
    @staticmethod
    def format_status_codes_display(status_codes: Dict[str, int]) -> str:
        """Format status codes for display with colors"""
        if not status_codes:
            return "Không có dữ liệu"
        
        # Sort by status code
        sorted_codes = sorted(status_codes.items(), key=lambda x: x[0])
        
        formatted_parts = []
        for code, count in sorted_codes:
            # Add emoji based on status code category
            if code.startswith('2'):
                emoji = "🟢"  # Success
            elif code.startswith('3'):
                emoji = "🟡"  # Redirect
            elif code.startswith('4'):
                emoji = "🔴"  # Client Error
            elif code.startswith('5'):
                emoji = "🔴"  # Server Error
            else:
                emoji = "⚪"  # Other
            
            formatted_parts.append(f"{emoji}{code}:{count}")
        
        return "; ".join(formatted_parts)
    
    @staticmethod
    def create_summary_stats(results: List[Dict]) -> Dict:
        """Create summary statistics from analysis results"""
        if not results:
            return {}
        
        total_domains = len(results)
        successful = sum(1 for r in results if not r.get('error'))
        failed = total_domains - successful
        
        total_snapshots = sum(r.get('total_snapshots', 0) for r in results if not r.get('error'))
        total_months = sum(r.get('month_col', 0) for r in results if not r.get('error'))
        
        # Status code distribution
        all_status_codes = {}
        for result in results:
            if not result.get('error') and result.get('status_codes'):
                for code, count in result['status_codes'].items():
                    all_status_codes[code] = all_status_codes.get(code, 0) + count
        
        return {
            'total_domains': total_domains,
            'successful_analyses': successful,
            'failed_analyses': failed,
            'success_rate': (successful / total_domains * 100) if total_domains > 0 else 0,
            'total_snapshots': total_snapshots,
            'total_unique_months': total_months,
            'avg_snapshots_per_domain': total_snapshots / successful if successful > 0 else 0,
            'avg_months_per_domain': total_months / successful if successful > 0 else 0,
            'status_code_distribution': all_status_codes
        }
    
    @staticmethod
    def export_to_csv(results: List[Dict], include_errors: bool = True) -> str:
        """Export results to CSV format"""
        if not results:
            return ""
        
        # Create DataFrame
        data = []
        for i, result in enumerate(results, 1):
            # Skip failed analyses if requested
            if not include_errors and result.get('error'):
                continue
            
            # Format time range
            time_range = ""
            if result.get('first_date') and result.get('last_date'):
                time_range = f"{result['first_date']} - {result['last_date']}"
            elif result.get('first_date'):
                time_range = result['first_date']
            
            # Format status codes (without emojis for CSV)
            status_codes = result.get('status_codes', {})
            status_display = "; ".join([f"{code}:{count}" for code, count in sorted(status_codes.items())])
            
            row = {
                'STT': i,
                'Domain': result['domain'],
                'Month_Col': result.get('month_col', 0),
                'Total_Snapshots': result.get('total_snapshots', 0),
                'Time_Range': time_range,
                'Status_Codes': status_display,
                'First_Date': result.get('first_date', ''),
                'Last_Date': result.get('last_date', ''),
                'Error': result.get('error', '')
            }
            data.append(row)
        
        # Convert to DataFrame and then CSV
        df = pd.DataFrame(data)
        return df.to_csv(index=False, encoding='utf-8')
    
    @staticmethod
    def export_to_excel(results: List[Dict], include_errors: bool = True) -> bytes:
        """Export results to Excel format"""
        if not results:
            return b""
        
        # Create DataFrame similar to CSV export
        data = []
        for i, result in enumerate(results, 1):
            if not include_errors and result.get('error'):
                continue
            
            time_range = ""
            if result.get('first_date') and result.get('last_date'):
                time_range = f"{result['first_date']} - {result['last_date']}"
            elif result.get('first_date'):
                time_range = result['first_date']
            
            status_codes = result.get('status_codes', {})
            status_display = "; ".join([f"{code}:{count}" for code, count in sorted(status_codes.items())])
            
            row = {
                'STT': i,
                'Domain': result['domain'],
                'Month Col': result.get('month_col', 0),
                'Total Snapshots': result.get('total_snapshots', 0),
                'Time Range': time_range,
                'Status Codes': status_display,
                'First Date': result.get('first_date', ''),
                'Last Date': result.get('last_date', ''),
                'Error': result.get('error', '')
            }
            data.append(row)
        
        # Create Excel file in memory
        df = pd.DataFrame(data)
        
        from io import BytesIO
        excel_buffer = BytesIO()
        
        with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='SEO Analysis')
            
            # Get workbook and worksheet for formatting
            workbook = writer.book
            worksheet = writer.sheets['SEO Analysis']
            
            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        excel_buffer.seek(0)
        return excel_buffer.getvalue()
    
    @staticmethod
    def save_temp_results(results: List[Dict], filename: str = None) -> str:
        """Save temporary results to JSON file"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"temp_results_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            return filename
        except Exception as e:
            raise ValueError(f"Error saving temp results: {str(e)}")
    
    @staticmethod
    def load_temp_results(filename: str) -> List[Dict]:
        """Load temporary results from JSON file"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            raise ValueError(f"Error loading temp results: {str(e)}")
